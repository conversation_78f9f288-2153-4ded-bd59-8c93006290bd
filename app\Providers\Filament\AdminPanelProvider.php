<?php

namespace App\Providers\Filament;

use Agencetwogether\HooksHelper\HooksHelperPlugin;
use App\Filament\Resources\CmsResource;
use App\Filament\Resources\KonfigResource;
// use App\Filament\Widgets\IkonWidget;
use App\Filament\Resources\MenuWebsiteResource;
use App\Filament\Widgets\InfoWidget;
use App\Filament\Widgets\RajaDesainerWidget;
use App\Filament\Widgets\RouteWidget;
use App\Models\Konfig;
use App\Models\Toko;
use App\Models\User;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use Datlechin\FilamentMenuBuilder\FilamentMenuBuilderPlugin;
use Datlechin\FilamentMenuBuilder\MenuPanel\StaticMenuPanel;
use DutchCodingCompany\FilamentDeveloperLogins\FilamentDeveloperLoginsPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use Modules\RajaMenu\Plugins\RajaMenuPlugin;
use ShuvroRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackupPlugin;
use Solutionforest\FilamentScaffold\FilamentScaffoldPlugin;


class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {

        $webInfo = $this->webInfo();

        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->sidebarWidth('15rem')
            ->navigation(true)
            ->topNavigation()
            ->brandName($webInfo['judul'])
            ->brandLogo($webInfo['logo'])
            ->favicon($webInfo['favicon'])
            ->brandLogoHeight('3rem')
            ->renderHook(
                'panels::page.header.actions.before',
                fn() => view('topbar')
            )
            // ->renderHook(
            //     // 'panels::topbar.after',
            //     'panels::global-search.before',
            //     fn() => view('rajamenu::navigation.auto-flyout')
            // )
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->colors([
                'primary' => Color::Amber,
            ])
            ->navigationGroups([

                NavigationGroup::make('Cms'),
                NavigationGroup::make('Aplikasi'),
                NavigationGroup::make('Pengaturan'),
                NavigationGroup::make('System'),
            ])


            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            // ->discoverResources(in: base_path('Modules/RajaJson/src/Resources'), for: 'Modules\\RajaJson\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            // ->discoverClusters(in: app_path('Filament/Clusters'), for: 'App\\Filament\\Clusters')
            ->pages([
                \App\Filament\Pages\Dashboard::class,

            ])
            // ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                // Widget ini didaftarkan tapi tidak ditampilkan di dashboard
                // Hanya digunakan di form PermissionResource dan di tempat lain
                // RouteWidget::class,
                // IkonWidget::class,
                InfoWidget::class,
                // RajaDesainerWidget::class,
            ])
            ->middleware([

                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,

            ])

            ->plugins([

                FilamentScaffoldPlugin::make(),


                FilamentDeveloperLoginsPlugin::make()
                    ->enabled()
                    ->users(fn() => User::pluck('email', 'name')->toArray()),
                FilamentMenuBuilderPlugin::make()

                    ->addLocation('website_header', 'website_header')

                    ->showCustomTextPanel()

                    ->addMenuPanels([
                        StaticMenuPanel::make('Default')
                            ->add('Home', url('/'))
                            ->add('Artikel', url('/artikel')),
                    ])
                    ->addMenuPanels([
                        StaticMenuPanel::make('halaman')
                            ->addMany(
                                \App\Models\Cms::where('jenis', 'halaman')
                                    ->pluck('slug', 'judul')
                                    ->map(fn($slug) => "/halaman/{$slug}")
                                    ->toArray()
                            )
                            ->description('Lorem ipsum...')
                            ->icon('heroicon-m-link')
                            ->collapsed(true)
                            ->collapsible(true)
                            ->paginate(perPage: 5, condition: true)
                    ])->usingResource(MenuWebsiteResource::class),

                    
                BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: false, // Sets the 'account' link in the panel User Menu (default = true)
                        userMenuLabel: 'Profileku', // Customizes the 'account' link label in the panel User Menu (default = null)
                        shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                        navigationGroup: 'Pengaturan', // Sets the navigation group for the My Profile page (default = null)
                        hasAvatars: false, // Enables the avatar upload form component (default = false)
                        slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                    ),

                FilamentSpatieLaravelBackupPlugin::make()->usingPage(\App\Filament\Pages\Backup::class)->usingQueue('backupanku')->noTimeout(),
                \Croustibat\FilamentJobsMonitor\FilamentJobsMonitorPlugin::make(),



                \TomatoPHP\FilamentPlugins\FilamentPluginsPlugin::make()
                    ->discoverCurrentPanelOnly(),

                \Visualbuilder\EmailTemplates\EmailTemplatesPlugin::make(),

                FilamentShieldPlugin::make()
                    ->gridColumns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 3
                    ])
                    ->sectionColumnSpan(1)
                    ->checkboxListColumns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 4,
                    ])
                    ->resourceCheckboxListColumns([
                        'default' => 1,
                        'sm' => 2,
                    ]),

                     HooksHelperPlugin::make(),

                      RajaMenuPlugin::make()
                
                ->auto(),

            ])->userMenuItems([
                'Nama' =>   MenuItem::make()
                    ->label(fn() => Auth::user()->name)
                    ->url(fn() => url('/admin/my-profile'))
                    ->sort(1)
                    ->icon('heroicon-o-user'),
                MenuItem::make('kasir')
                    ->label('Admin Kasir')
                    // ->icon('tabler-cash-register')
                    ->url(fn(): string => url('/adminkasir'))
                    ->openUrlInNewTab(true)
                    ->visible(fn() => $this->cekAplikasiAktif('Kasir'))
                    ->sort(2),
                MenuItem::make('hotel')
                    ->label('Admin Hotel')
                    // ->icon('tabler-building-skyscraper')
                    ->url(fn(): string => url('/adminhotel'))
                    ->openUrlInNewTab(true)
                    ->visible(fn() => $this->cekAplikasiAktif('Hotel'))
                    ->sort(3),

            ])
            ;
    }



    /**
     * Memeriksa apakah aplikasi ada dan aktif
     *
     * @param string $namaAplikasi Nama folder aplikasi
     * @return bool
     */
    protected function cekAplikasiAktif(string $namaAplikasi): bool
    {
        $aplikasiPath = app_path("Aplikasi/{$namaAplikasi}");
        $configPath = $aplikasiPath . '/config.php';

        if (file_exists($configPath)) {
            $config = include $configPath;
            return isset($config['status']) && $config['status'] === 'aktif';
        }

        return false;
    }

    protected function webInfo()
    {
        $toko = Konfig::jcolObject('website');

        // Sekarang bisa menggunakan property object
        $brandName = $toko->judul ?? 'Admin Panel';
        $brandLogo = isset($toko->logo) ? url('storage/' . $toko->logo) : null;
        $favicon = isset($toko->favicon) ? url('storage/' . $toko->favicon) : null;


        return [
            'judul' => $brandName,
            'deskripsi' => $toko->deskripsi ?? 'Sistem manajemen hotel',
            'keywords' => $toko->keywords ?? 'hotel, manajemen, sistem',
            'favicon' => $favicon,
            'logo' => $brandLogo,
        ];
    }


}
